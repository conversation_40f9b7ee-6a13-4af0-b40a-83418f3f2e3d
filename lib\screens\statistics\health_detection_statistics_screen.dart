import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../constants/health_detection.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 健康监测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() =>
      _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState
    extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.multiLine;
  DateTime _currentDate = DateTime.now();
   Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  /// 多曲线图中各健康监测项目的可见性控制
  Map<HealthDetection, bool> _visibleDetections = {
    for (HealthDetection detection in HealthDetection.values)
      detection: true,
  };

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false).device?.deviceName ?? '';
    ChartService chartService = Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchHealthDetectionChartData(device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchHealthDetectionChartData(device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchHealthDetectionChartData(device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康监测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动切换图表类型
                final availableTypes =
                    ChartTypePresets.getHealthDetectionStatistics(period);
                if (availableTypes.isNotEmpty &&
                    !availableTypes.contains(_selectedChartType)) {
                  _selectedChartType = availableTypes.first;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表类型选择器
          ChartViewSelector(
            availableTypes:
                ChartTypePresets.getHealthDetectionStatistics(_selectedPeriod),
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),

          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '健康监测趋势',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),

          // 统计信息
          //if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.multiLine:
        return _buildMultiLineChart();
      case ChartViewType.horizontalBar:
        return _buildHorizontalBarChart();
      default:
        return _buildMultiLineChart();
    }
  }

  /// 聚合健康监测数据按时间分组
  Map<HealthDetection, List<FlSpot>> _aggregateHealthDetectionData() {
    final Map<HealthDetection, List<FlSpot>> result = {};
    // 初始化所有监测项目的数据列表
    for (final detection in HealthDetection.values) {
      double x = 0;
      for (final data in _healthData[detection.name]) {
        result[detection] ??= [];
        result[detection]!.add(FlSpot(x, data.toDouble()));
        x++;
      }
    }
    return result;
  }





  /// 构建多曲线图（健康监测趋势）
  Widget _buildMultiLineChart() {
    final aggregatedData = _aggregateHealthDetectionData();

    // 过滤出可见的监测项目数据
    final visibleData = <HealthDetection, List<FlSpot>>{};
    for (final entry in aggregatedData.entries) {
      if (_visibleDetections[entry.key] == true && entry.value.isNotEmpty) {
        visibleData[entry.key] = entry.value;
      }
    }

    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: 2.0, // 设置宽高比为2:1
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade300,
                      strokeWidth: 1,
                      dashArray: [5, 5], // 设置虚线样式
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                      interval: 12.0, // 每12个点显示一次标签（6小时间隔）
                      getTitlesWidget: (value, meta) {
                        return Padding(
                          padding:
                              const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                          child: Text(
                            _getBottomTitle(value.toInt()),
                            style: TextStyle(
                                fontSize: 10, color: Colors.grey.shade600),
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 25, // 进一步减少右侧预留空间，让绘图区域更靠近右边缘
                      interval: _getYAxisInterval(visibleData), // 设置Y轴间隔
                      getTitlesWidget: (value, meta) {
                        final interval = _getYAxisInterval(visibleData).toInt();
                        final intValue = value.toInt();

                        // 只显示符合间隔的非负整数值
                        if (intValue >= 0 && interval > 0 && intValue % interval == 0) {
                          return Text(
                            intValue.toString(),
                            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                            textAlign: TextAlign.right,
                          );
                        }
                        return SizedBox.shrink(); // 不显示非间隔值
                      },
                    ),
                  ), // 显示右侧数值标题
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false), // 去掉坐标轴外框
                lineTouchData: LineTouchData(
                  enabled: true,
                  handleBuiltInTouches: true, // 确保内置触摸处理正常
                  touchTooltipData: LineTouchTooltipData(
                    getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                      return _buildMultiLineTooltipItems(touchedBarSpots);
                    },
                  ),
                ),
                lineBarsData: _buildMultiLineChartBars(visibleData),
              ),
            ),
          ),
        ),
        SizedBox(height: 16),
        // 底部图例
        _buildBottomLegend(),
      ],
    );
  }

  /// 获取Y轴最大值（用于多曲线图）
  double _getMaxYValue(Map<HealthDetection, List<FlSpot>> visibleData) {
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 获取Y轴间隔（用于多曲线图）
  double _getYAxisInterval(Map<HealthDetection, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 1.0;

    final maxValue = _getMaxYValue(visibleData);

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建多曲线图的曲线数据
  List<LineChartBarData> _buildMultiLineChartBars(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    final List<LineChartBarData> lineBars = [];

    for (final entry in visibleData.entries) {
      final detection = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

      lineBars.add(
        LineChartBarData(
          spots: spots,
          isCurved: true, // 使用光滑曲线
          curveSmoothness: 0.15, // 设置曲线平滑度，值越大越平滑
          color: color,
          barWidth: 1.5, // 线条宽度
          dotData: FlDotData(show: false), // 不显示曲线上的小圆点
          belowBarData: BarAreaData(show: false), // 多曲线图不显示填充区域
        ),
      );
    }

    return lineBars;
  }

  /// 构建多曲线图的Tooltip项
  List<LineTooltipItem> _buildMultiLineTooltipItems(
      List<LineBarSpot> touchedBarSpots) {
    final List<LineTooltipItem> tooltipItems = [];

    if (touchedBarSpots.isNotEmpty) {
      final index = touchedBarSpots.first.x.toInt();
      final timeRange = _getBottomTitle(index);

      // 为每个触摸点创建对应的tooltip项
      final visibleDetections = HealthDetection.values
          .where((detection) => _visibleDetections[detection] == true)
          .toList();

      for (int i = 0; i < touchedBarSpots.length; i++) {
        final barSpot = touchedBarSpots[i];

        if (i < visibleDetections.length) {
          final detection = visibleDetections[i];
          final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

          // 第一个tooltip项包含时间范围，其他项只显示监测信息
          final displayText = i == 0
              ? '$timeRange\n${detection.displayName}: ${barSpot.y.toInt()}'
              : '${detection.displayName}: ${barSpot.y.toInt()}';

          tooltipItems.add(
            LineTooltipItem(
              displayText,
              TextStyle(
                color: i == 0 ? Colors.white : color,
                fontWeight: i == 0 ? FontWeight.bold : FontWeight.w600,
                fontSize: i == 0 ? 12 : 11,
              ),
            ),
          );
        } else {
          // 如果没有对应的监测项目，添加空的tooltip项以保持数量一致
          tooltipItems.add(
            LineTooltipItem(
              '',
              const TextStyle(color: Colors.transparent),
            ),
          );
        }
      }
    }

    return tooltipItems;
  }

  /// 构建水平条形图（各类健康监测的出现次数）
  Widget _buildHorizontalBarChart() {
    // 统计各类健康监测的出现次数
    final detectionCounts = <HealthDetection, int>{};

    for(final status in HealthDetection.values){
      detectionCounts[status] = _healthData[status.name]!.fold(0, (prev, element) => prev + element);
    }

    // 确保所有10种监测项目都有数据（没有的设为0）
    for (final detection in HealthDetection.values) {
      detectionCounts.putIfAbsent(detection, () => 0);
    }

    if (detectionCounts.values.every((count) => count == 0)) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    // 按照固定顺序排列（从上到下）：发热、呼吸异常、运动不稳、步态不对称、步态规律性下降、夜醒、睡眠不足、运动复杂度异常、活动模式改变、kcal下降
    final sortedEntries = HealthDetection.values.map((detection) {
      return MapEntry(detection, detectionCounts[detection]!);
    }).toList();

    // 反转顺序，使发热在顶部
    // final reversedEntries = sortedEntries.reversed.toList();
    final reversedEntries = sortedEntries;

    // 计算最大值用于比例计算
    final maxCount =
        reversedEntries.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8), // 减少padding
        child: Column(
          children: reversedEntries.map((entry) {
            final detection = entry.key;
            final count = entry.value;
            final color =
                Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
            final percentage = maxCount > 0 ? count / maxCount : 0.0;

            return Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 0.5), // 进一步减少间距
                child: Row(
                  children: [
                    // 条形图区域
                    Expanded(
                      child: Stack(
                        children: [
                          // 背景条
                          Container(
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          // 数据条（彩色条纹，不包含标签）
                          FractionallySizedBox(
                            widthFactor: percentage,
                            child: Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          // 标签（在浅灰色背景的最左侧，最顶层显示）
                          Positioned(
                            left: 8,
                            top: 0,
                            bottom: 0,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                detection.displayName, // 在浅灰色背景最左侧的标签
                                style: TextStyle(
                                  color: Colors.white, // 白色文字，在最顶层显示
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 8),
                    // 外部数值显示（右侧）
                    SizedBox(
                      width: 30,
                      child: Text(
                        '$count',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建底部图例
  Widget _buildBottomLegend() {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: HealthDetection.values.map((detection) {
        final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
        final isVisible = _visibleDetections[detection] ?? true;

        return GestureDetector(
          onTap: () {
            setState(() {
              _visibleDetections[detection] = !isVisible;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isVisible
                  ? color.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isVisible ? color : Colors.grey,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8, // 缩小到30%（原来是12）
                  height: 8,
                  decoration: BoxDecoration(
                    color: isVisible ? color : Colors.grey,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 4),
                Text(
                  detection.displayName,
                  style: TextStyle(
                    fontSize: 10,
                    color: isVisible
                        ? Colors.grey.shade700
                        : Colors.grey.shade500,
                    fontWeight:
                        isVisible ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    List<String> weekdaysShort = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = index ~/ 4;
        final minute = (index % 4) * 15;
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return weekdaysShort[index];
      case TimePeriod.month:
        return '${index+1}日';
    }
  }


}
